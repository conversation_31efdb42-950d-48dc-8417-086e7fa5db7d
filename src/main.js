import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import moment from 'moment';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.less';
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
import directive from '@/global/directive';
import { getDicts } from '@/api/system/dict';
import CustomComponent from '@/global/component';
import bootstrap from '@/bootstrap';
import './global/style/index.less';
import ProComponents from '@bangdao/pro-components';
import { BuseCrud, BuseRangePicker, AutoFilters } from '@bangdao/buse-components';

Vue.use(ProComponents);
Vue.use(BuseCrud);
Vue.use(BuseRangePicker);
Vue.use(AutoFilters);

// 字典数据组件
import DictData from '@/components/DictData';
// 全局方法挂载
Vue.prototype.getDicts = getDicts;

// 字典数据组件
import useDict from '@/utils/system/hooks/dict';
// 全局方法挂载
Vue.prototype.useDict = useDict;

// 使用moment中文语言包
moment.locale('zh-cn');

// 注册antd-vue组件库
Vue.use(Antd);

// 注册vxe-table组件库
Vue.use(VXETable);

// 注册自定义指令
Vue.use(directive);

// 注册自定义组件
Vue.use(CustomComponent);

bootstrap({ router, store, message: Vue.prototype.$message });

DictData.install();

const debounce = (callback, delay) => {
  let tid;
  return function () {
    const ctx = self;
    tid && clearTimeout(tid);
    tid = setTimeout(() => {
      callback.apply(ctx, arguments);
    }, delay);
  };
};

export default () => {
  const _ = window.ResizeObserver;
  window.ResizeObserver = class ResizeObserver extends _ {
    constructor(callback) {
      callback = debounce(callback, 20);
      super(callback);
    }
  };
};

Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount('#app');
