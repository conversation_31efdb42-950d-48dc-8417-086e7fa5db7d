import request from '@/utils/system/request';

// 质检模板下拉列表
export function listAllAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/check/template/listAll',
    method: 'post',
    data: params,
  });
}

// 通话质检
export function checkAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/check',
    method: 'post',
    data: params,
  });
}

// 通话质检详情
export function checkDetailAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/checkDetail?communicateId=' + params,
    method: 'get',
  });
}

// 通话质检申诉
export function checkAppealAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/checkAppeal',
    method: 'post',
    data: params,
  });
}

// 通话质检申诉审核
export function reviewAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/checkAppeal/review',
    method: 'post',
    data: params,
  });
}

// 通话质检申诉审核
export function checkListAPI(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/checkList',
    method: 'post',
    data: params,
  });
}

//左侧的详细信息，以及时间流程
export function phoneDetail(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/detail?mainUniqueId=' + params,
    method: 'get',
  });
}

//访客详情
export function detailAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/visitor/detail',
    method: 'post',
    data: params,
  });
}

//录音获取
export function voiceFn(params) {
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/tr/recordFile?mainUniqueId=' + params,
    method: 'get',
  });
}
//导出
export function exportAPI(params) {
  console.log(params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/communicate/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}
