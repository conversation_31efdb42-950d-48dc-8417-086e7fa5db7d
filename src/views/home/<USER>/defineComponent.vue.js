import ThirdTab from './components/thirdTab';
import { formConfig } from '../second/constant';
import Editor from '@/components/Editor/ProductEditor';
import Tags from '@/views/home/<USER>';
import { DictCodeAPI } from '@/api/system/dict';
import { mapState } from 'vuex';
import {
  detailAPI,
  editVisitorAPI,
  orderListAPI,
  orderDraftAPI,
  checkPhone,
  stationList,
  maintenanceList,
  retry,
} from '@/api/system/callBench';
import * as api from '@/api/system/callBench';
import province from '@/views/home/<USER>/province';
import moment from 'moment';
import { labelFindFn, setCustomSearchTree, valueFindFn, cutString, getFatherBySon } from '@/utils/system/common/index';
import { defaultDict } from '@/utils/system/common/index';
import AddReply from '@/components/Editor/AddReply';
import NewOrder from '@/views/home/<USER>';
import StatuNode from '@/components/TreeNode/StatuNode';

import {
  workTypeAPI,
  listAPI,
  flowTypeAPI,
  userListAPI,
  getAutoAllocationAPI,
  callListAPI,
  addAPI,
} from '@/api/system/callBench';
import { editOrder } from '@/api/system/workorder';

export default {
  name: 'DumiDocVueIndex',
  components: {
    ThirdTab,
    Editor,
    Tags,
    AddReply,
    NewOrder,
    StatuNode,
  },
  data() {
    return {
      workType: [{}], //工单分类接收
      isWorkTypeFn: false, //工单分类是否选择过，避免重复发请求
      nowCategoryID: '', //当前分类受理id，用于递归判断
      isNullType: false, //判断当前接收是否没有受理人/组
      handleList: [], //没有受理组时候，用到的表
      handleUserList: [], //没有受理人时候，用到的表（先选受理组，才有受理人）
      // handleusers: { label: '', value: undefined }, //当前处理人
      // handlegroups: { label: '', value: undefined }, //当前处理组
      backupHandle: { userlabel: '', userValue: '', grouplabel: '', groupValue: '' }, //自动分配备份
      flowRadio: '1', //流转方式，默认人工，有设置就改
      enabledFlag: '1', //涉及到flowRadio的显示，一个开关，默认我开了"1"

      templateId: 0, //工单模板id
      exValues: [], //模板内的值
      exList: [
        {
          fields: [],
        },
      ], //模板分类后渲染样式接收（无值）
      modChoice: 0, //选择第几个模板，避免模板v-for嵌套
      choiceValue: undefined, //选择第几个模板,写这个避免报错
      showMod: false, //因为工单模板不是必选，所以需要一开始给一个不传值状态
      contentInput: '', //富文本框内容1
      secContentInput: '', //富文本框内容2
      orderTags: [], //工单标签，来自guestTags组件
      orderName: [], //工单标签名称
      showTags: false, //是否展示Tags的select框;同时控制按钮

      showGuest: false,
      isAttention: 1,
      man: false, //先生
      lady: false, //女士
      gen: 0, //这个是给重复输入避免丢失先生/女士准备的
      visitorSex: 0, //访客性别
      menuShow: false,
      tableData: [],
      loading: false,
      leftPage: 1, //左侧分页
      leftPageCount: 10, //左侧分页每页数量
      flag: 1, //默认tab
      leftSize: 10, //左侧每页显示
      leftChoice: '', //左侧选中后出现的样式，根据独特的id判断
      phoneList: [{}], //左侧通话列表数据
      nowTab: 1, //现在处于哪个标签
      leftIsSelect: false, //左侧没有选择的话，右侧作不显示处理？
      modalSeen: false, //弹窗的显示与否，作用与flowRadio的是否紧急提示
      prevFlow: '3', //保存的紧急
      nowFlow: '3', //现在的紧急
      showRight: false, //还没选择左侧通话记录时候，false不显示
      detailRight: {}, //右侧content访客详细信息
      fromName: '', //右侧content访客来源内容
      tagName: [], //右侧content访客标签内容
      fromGrade: '', //右侧content访客等级内容
      customerNumber: '', //这个和下面的用来备份，给content的刷新按钮使用
      customerProvince: '', //省
      customerCity: '', //市
      firstFont: '', //第一个字，直接用名字[0]在未接收时候会报错，无奈
      inputTel: '', //打电话输入框
      isRightWrite: false, //content访客详情编辑是否打开
      defaultAddress: '', //用来储存content地址的字符串
      noDotAddress: '',
      date: '', //moment第一次加载时获取日期，用于给本月方法使用

      provinceList: [], //省份

      key: '', //右侧tab的key，用于刷新
      visitorRules: {}, // 访客规则

      //字典部分
      visitorGradeSelect: [{ label: '', value: '' }], //访客等级处理数组
      visitorFromSelect: [], //真实来源处理数组
      tagSelect: [], //标签处理数组
      selectOne: [], //模板里面的下拉对象数组
      selectCom: [], //模板下拉多选
      rules: {}, //规则，用于prop
      createFlag: '', //当前选择创建工单的状态，用于判断是否草稿
      showFlowType: false,
      orderStatus: '', //工单状态颜色

      showButton: false, //本月和本周的高亮，false高亮全部，true高亮本周

      backName: '', //备份名字
      autoFlag: false, //禁用自动分配

      tempRules: {},

      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        //列名，列数字转文字配置
        {
          field: 'orderId',
          title: '工单号',
          slots: { default: 'orderId' },
          width: 168,
        },
        {
          field: 'createByName',
          title: '提交人',
          width: 110,
        },
        {
          field: 'urgency',
          title: '优先级',
          slots: { default: 'urgency' },
          width: 90,
        },
        {
          field: 'categoryName',
          title: '分类',
          slots: { default: 'categoryName' },
          minWidth: 140,
        },
        {
          field: 'orderStatus',
          title: '工单状态',
          slots: { default: 'orderStatus' },
          minWidth: 125,
        },
        {
          field: 'orderSource',
          title: '工单来源',
          minWidth: 120,
        },
        {
          field: 'flowType',
          title: '流转方式',
          minWidth: 120,
        },
        {
          field: 'phoneNumber',
          title: '手机号',
          width: 128,
        },
        {
          field: 'createTime',
          title: '提交时间',
          minWidth: 180,
        },
        {
          field: 'handleUserName',
          title: '当前处理人',
          minWidth: 108,
        },
      ],
      tableList: {
        id: '',
        priority: '',
        classify: '',
        status: '',
        from: '',
        transMod: '',
        tel: '',
        subTime: '',
        conductor: '',
      }, //表新增参数
      showTemp: false, //模板哪一行展示
      autoDisable: false, //受理人禁止选择
      canSub: true,
      stationOptions: [], //站点
      maintenanceOptions: [], //运维受理人
      showFailModal: false, //工单创建失败弹窗
      failOrderId: undefined,
      modalLoading: false,
      isLoading: false,
      currentPage: 1,
      stationValue: '',
      stationRequired: false,
      editStationId: '',
      editStationName: '',
      failMsg: '同步创建工单失败',
    };
  },
  computed: {
    ...mapState({
      merchant: (state) => state.base.merchant || {},
    }),
    modalConfig() {
      let newSelect = this.test;
      formConfig.push(newSelect);
      //弹窗类配置
      return {
        okText: '保存草稿',
        okBtnVerify: false,
        formConfig: [
          {
            field: 'workTypeId',
            title: '工单分类',
            element: 'a-tree-select',
            props: {
              // fieldNames: { label: 'categoryName', value: 'categoryId', children: 'children' },
              // changeOnSelect: true,
              // options: this.workType,
              treeData: this.workType,
              showSearch: true,
              replaceFields: {
                title: 'categoryName',
                value: 'categoryId',
              },
              treeNodeFilterProp: 'title',
              getPopupContainer: (triggerNode) => triggerNode.parentNode,
            },
            on: {
              //工单分类选择
              change: async (value) => {
                // const filteredArr = value.filter((item) => typeof item !== 'undefined');
                this.nowCategoryID = value;
                this.flowTypeFn();
                // this.findCategoryId(this.workType, this.nowCategoryID); //是否为空代理人
                this.showTemp = false;
                await this.getMod();
                if (this.exList && this.exList.length > 0) {
                  this.showTemp = true;
                  await this.handleChange(0);
                }
                // this.handleusers = { label: '', value: undefined }; //换分类清空当前处理受理组
                // this.handlegroups = { label: '', value: undefined };
              },
              click: () => {
                this.workTypeFn();
                console.log('触发了点击获取工单分类');
              },
            },
            rules: [{ required: this.canSub, message: '请选择工单分类' }],
          },
          {
            field: 'phoneNumber',
            title: '工单手机号',
            defaultValue: this.customerNumber,
            rules: [
              { required: this.merchant.merchantId != '40002', message: '请输入正确的电话号码', pattern: /^\d+$/ },
            ],
            show: this.merchant.merchantId != '40002',
          },
          {
            field: 'orderContent',
            title: '工单内容',
            element: 'slot',
            slotName: 'orContent',
            rules: [{ required: this.canSub, message: '请输入工单内容' }],
          },
          {
            field: 'phoneNumber',
            title: '用户手机号',
            defaultValue: this.customerNumber,
            // rules: [
            //   { required: this.merchant.merchantId == '40002', message: '请输入正确的电话号码', pattern: /^\d+$/ },
            // ],
            //租户id为目的地租户40002时展示几个新增字段
            show: this.merchant.merchantId == '40002',
          },
          {
            field: 'userName',
            title: '用户姓名',
            element: 'a-input',
            rules: [{ max: 100, message: '100字符以内' }],
            show: this.merchant.merchantId == '40002',
          },
          {
            field: 'city',
            title: '所在城市',
            element: 'a-input',
            rules: [{ max: 200, message: '200字符以内' }],
            show: this.merchant.merchantId == '40002',
          },
          {
            field: 'stationId',
            title: '站点名称',
            // element: 'slot',
            // slotName: 'stationId',
            element: 'a-select',
            props: {
              options: this.stationOptions,
              showSearch: true,
              // filterOption: (input, option) => {
              //   return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              // },
              filterOption: false,
            },
            on: {
              popupScroll: (event) => {
                // console.log(event);
                const { scrollTop, offsetHeight, scrollHeight } = event.target;
                if (scrollTop + 2 + offsetHeight >= scrollHeight) {
                  // 检测到滚动到底部
                  this.getStationOptions();
                }
              },
              search: async (val) => {
                this.stationValue = val;
                this.stationOptions = [];
                this.currentPage = 1;
                await this.getStationOptions();
              },
            },
            show: this.merchant.merchantId == '40002',
            rules: [
              { required: this.stationRequired && this.merchant.merchantId == '40002', message: '请选择站点名称' },
            ],
          },
          {
            field: 'vehicle',
            title: '车型品牌',
            element: 'slot',
            slotName: 'vehicle',
            rules: [{ max: 100, message: '100字符以内' }],
            show: this.merchant.merchantId == '40002',
          },
          {
            field: 'orderStatus',
            title: '工单状态',
            element: 'a-select',
            props: {
              //工单状态列表（1-处理中 2-待回访 3-催办中 4-技术处理中 5-无需处理 6-已处理 7-已完结 8-运营商处理中）
              options: [
                { label: '处理中', value: '1' },
                { label: '处理中（重新处理）', value: '11' },
                { label: '待回访', value: '2' },
                // { label: '催办中', value: '3' },
                // { label: '技术处理中', value: '4' },
                { label: '无需处理', value: '5' },
                // { label: '已处理', value: '6' },
                { label: '已完结', value: '7' },
                // { label: '运营商处理中', value: '8' },
              ],
              getpopupcontainer: (triggerNode) => triggerNode.parentNode,
            },
            rules: [{ required: this.canSub, message: '请选择工单状态' }],
            on: {
              select: (value) => {
                this.showFlowType = value === '7' ? false : true;
                this.stationRequired = value == '7' || value == '2' ? false : true;
              },
              change: () => {
                this.flowTypeFn();
              },
            },
          },
          {
            field: 'urgency',
            title: '工单优先级',
            element: 'a-radio-group',
            defaultValue: '3',
            props: {
              options: [
                { label: '非常紧急', value: '1' },
                { label: '紧急', value: '2' },
                { label: '一般', value: '3' },
                { label: '低', value: '4' },
              ],
              rules: [{ required: true, message: '请选择优先级' }],
            },
            on: {
              change: (value) => {
                console.log('紧急程度', value);
                if (value == '1' || value == '2') {
                  this.nowFlow = value;
                  this.modalSeen = true;
                } else {
                  this.prevFlow = value;
                }
              },
            },
          },
          {
            field: 'flowType',
            title: '流转方式',
            element: 'slot',
            slotName: 'flowType',
            props: {
              rules: [{ required: true, message: '请选择流转方式' }],
            },
            show: this.showFlowType,
          },
          {
            field: 'maintenanceUser',
            title: '运维受理人',
            element: 'a-select',
            props: {
              options: this.maintenanceOptions,
              showSearch: true,
              filterOption: (input, option) => {
                return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              },
            },
            show: this.merchant.merchantId == '40002',
          },
          {
            field: 'orderHandler',
            title: '工单受理人',
            element: 'slot',
            slotName: 'orderHandler',
            show: this.flowRadioShow && this.showFlowType,
          },
          {
            field: 'orderTemplateId',
            title: '工单模板',
            element: 'slot',
            slotName: 'selectOr',
            show: this.showTemp,
          },
          {
            field: 'not',
            title: '创建新访客',
            element: 'slot',
            slotName: 'newGuest',
            colProps: {
              span: 24,
            },
          },
          // {
          //   field: 'visitorName',
          //   title: '访客名称',
          //   element: 'slot',
          //   slotName: 'guestName',
          //   show: this.showGuest,
          //   itemprops: { labelCol: { span: 4, offset: 4, }, wrapperCol: { span: 12, }, },
          //   rules: [{ required: this.showGuest, message: '请填写访客名称' }],

          // },
          // {
          //   field: 'visitorPhone',
          //   title: '访客电话',
          //   show: this.showGuest,
          //   itemprops: { labelCol: { span: 4, offset: 4, }, wrapperCol: { span: 12, }, },
          //   rules: [
          //     { required: this.showGuest, message: '输入电话号码格式不正确', pattern: /^\d+$/ },
          //     {
          //       validator: (rule, value, callback) => {
          //         let result = new Promise((resolve) => {
          //           resolve(checkPhone(value));
          //         });
          //         result.then((res) => {
          //           console.log('then', res[0]);
          //           if (res[0] === undefined) {
          //             callback('电话号码已经被占用');
          //           } else {
          //             callback();
          //           }
          //         });
          //       },
          //       trigger: 'blur',
          //     },
          //   ],

          // },
          {
            field: 'orderTags',
            title: '工单标签',
            element: 'slot',
            slotName: 'guestTag',
          },
        ],
        menuTitle: '操作',
        menu: false,
        addBtn: false,
        delBtn: false,
        viewBtn: true,
        editBtn: false,
        submitBtn: true,
      };
    },
    NumberChoice() {
      //数字转化
      return Number(this.modChoice);
    },
    //由于buse组件不支持show内部写函数，那就用computed实现flowType为3时不显示受理人和受理组
    flowRadioShow() {
      return this.flowRadio !== '3';
    },
    watchStatus() {
      return this.$store.state.base.isCall;
    },
    visitorSecPhone() {
      return this.detailRight.visitorSecPhone;
    },
    //嵌套太深导致出错，无可奈何
    choiceExList() {
      return this.exList[this.NumberChoice] || [];
    },
  },

  beforeMount() {
    this.leftChange(1);
    this.getProvince(); //本地省份获取
    this.getUserList();
    // this.getStationOptions();
    this.getMaintenanceOptions();
  },

  methods: {
    //创建工单失败-重试
    async resendOrder() {
      this.modalLoading = true;
      const method = this.failMsg === '同步创建工单台账失败' ? 'retryLedger' : 'retry';
      const [res] = await api[method]({ orderId: this.failOrderId });
      this.modalLoading = false;
      if (res?.success) {
        this.showFailModal = false;
        this.$success({
          title: res.data,
          okText: '我知道了',
        });
        this.leftChange(1);
      }
    },
    // 获取站点信息列表
    async getStationOptions() {
      if (this.isLoading) {
        return;
      }
      this.isLoading = true;
      const params = { pageNum: this.currentPage, pageSize: 10, stationName: this.stationValue };
      const [res] = await stationList(params);
      const newOptions = res?.data?.map((x) => {
        return { ...x, label: x.stationName, value: x.stationId };
      });
      if (newOptions.length > 0) {
        this.stationOptions = this.stationOptions.concat(newOptions);
        this.currentPage++;
      }
      console.log(this.stationOptions);
      this.isLoading = false;
    },
    //获取运维人员列表
    async getMaintenanceOptions() {
      const [res] = await maintenanceList({});
      this.maintenanceOptions = res?.data?.map((x) => {
        return { ...x, label: x.userName + '-' + x.nickName, value: x.userId };
      });
    },
    getUserList() {
      this.$store.dispatch('base/GetUserList', { nickName: '' }).then((res) => {
        this.handleList = setCustomSearchTree(res);
        console.log('handleList', this.handleList);
      });
    },
    urgencyColor(value) {
      if (value === '非常紧急') {
        return '#f5222d';
      } else if (value === '紧急') {
        return '#fa8c16';
      } else {
        return '#52c41a';
      }
    },
    // 工单状态转换颜色
    transformStatu(orderStatu) {
      let color = '';
      if (orderStatu === '催办中' || orderStatu === '9') {
        color = 'red';
      } else if (orderStatu === '无需处理' || orderStatu === '已处理' || orderStatu === '已完结') {
        color = 'green';
      } else {
        color = 'blue';
      }
      return color;
    },
    async loadData(isMonth) {
      // this.$message.info(`查询参数为：${JSON.stringify(this.params)}`);
      this.loading = true;
      let page = { pageNum: this.tablePage.currentPage, pageNumSize: this.tablePage.pageSize };
      let start = '';
      let end = '';
      if (isMonth) {
        let date = moment().format();
        start = moment(date).startOf('month').format('YYYY-MM-DD');
        end = moment(date).endOf('month').format('YYYY-MM-DD');
        console.log('现在时间？', date);
        console.log('获取这个月第一天', start);
        console.log('获取这个月最后一天', end);

        this.showButton = true;
      } else {
        this.showButton = false;
      }
      const [result] = await orderListAPI({
        ...page,
        visitorPhone: this.customerNumber,
        visitorSecPhone: this.detailRight.visitorSecPhone,
        startDate: start,
        endDate: end,
      });
      console.log('历史工单内容', result.data);

      this.tablePage.total = result.count;
      this.loading = false;
      this.tableData = result.data;
    },
    //递归判断分类时对应id下，是不是没有受理人。有就给固定值，没有就发请求给列表
    // findCategoryId(arr, categoryId) {
    //   for (let i = 0; i < arr.length; i++) {
    //     const obj = arr[i];

    //     // 首先检查当前对象的categoryId是否匹配
    //     if (obj.categoryId === categoryId) {
    //       if (obj.handleUser === '') {
    //         this.isNullType = true; //没有受理组
    //         console.log('受理人handlerUser为空，无自带');
    //         return true; // handlerUser为空
    //       } else {
    //         this.handlegroups.label = obj.handleGroupName;
    //         this.handlegroups.value = obj.handleGroup;
    //         this.handleusers.label = obj.handleUserName;
    //         this.handleusers.value = obj.handleUser;
    //         this.isNullType = false; //有受理组
    //         console.log('受理人handlerUser有自带的');
    //         return false; // handlerUser不为空
    //       }
    //     }

    //     // 如果当前对象有children属性，则递归遍历子对Ž
    //     if (obj.children && obj.children.length > 0) {
    //       const result = this.findCategoryId(obj.children, categoryId);
    //       if (result !== '') {
    //         return result;
    //       }
    //     }
    //   }

    //   // 如果没有找到匹配的对象，则返回undefined
    //   return '';
    // },

    //草稿按钮，这两个名字互换了
    async modalConfirmHandler(tablelist) {
      console.log('提交按钮');
      await this.modalSubmit(tablelist, 1);
    },

    //保存按钮
    async modalSubmit(param, commitFlag) {
      let { vehicleYear, ...tableList } = param;
      tableList.vehicleYear = vehicleYear?.startValue ? moment(vehicleYear.startValue).format('yyyy') : '';
      tableList.orderSource = '1'; //手工录入
      tableList.maintenanceUserName = this.maintenanceOptions?.find(
        (x) => x.value == tableList.maintenanceUser
      )?.nickName;
      //编辑时站点名称如果没有修改，则保留原来的id和name
      if (tableList.stationId === this.editStationName) {
        tableList.stationId = this.editStationId;
        tableList.stationName = this.editStationName;
      } else {
        tableList.stationName = this.stationOptions?.find((x) => x.value == tableList.stationId)?.stationName;
      }
      console.log(tableList, '提交！');
      let tempFlag = true;
      let visitorFlag = true;
      if (this.showGuest) {
        try {
          await this.$refs.visitorForm.validate();
        } catch {
          visitorFlag = false;
        }
      }
      if (this.showMod && this.showTemp) {
        try {
          await this.$refs.templateForm.validate();
        } catch {
          tempFlag = false;
        }
        const exFields = this.exList[this.NumberChoice];
        let choiceExList = [];
        if (exFields && exFields.fields && exFields.fields.length !== 0) {
          console.log('上传exList', this.exList);
          tableList.templateId = this.choiceExList.templateId;
          choiceExList = this.exList[this.NumberChoice].fields;

          for (let i = 0; i < this.exValues.length; i++) {
            choiceExList[i] = {
              ...choiceExList[i],
              value: this.exValues[i] || undefined,
            };
            // console.log('值塞进去了吗', this.exList[this.NumberChoice].fields[i].value);
          }
          console.log(choiceExList);
        }
        let halfLength = Math.floor(choiceExList.length / 2);
        choiceExList = choiceExList.splice(0, halfLength);

        tableList.field = choiceExList || []; //加入模版参数
        console.log('看看参数', tableList.field);
      } else {
        tableList.field = null;
      }
      if (!tempFlag || !visitorFlag) return false;
      tableList.categoryId = this.nowCategoryID;
      tableList.order = this.templateId;
      tableList.flowType = this.showFlowType ? this.flowRadio : '';
      tableList.mainUniqueId = this.leftChoice;
      tableList.visitorSex = this.visitorSex;

      tableList.commitFlag = commitFlag ? '0' : '1'; //新加的草稿需求，这里1代表上传而非草稿

      if (this.orderTags) {
        tableList.orderTags = JSON.parse(JSON.stringify(this.orderTags));
        tableList.orderTags = this.orderTags.join(',');
      } else {
        tableList.orderTags = this.orderTags;
      }

      //如果流转方式是3，那么到底会传入什么样的值？拭目以待。
      if (!this.flowRadioShow) {
        tableList.handleGroup = '';
        tableList.handleUser = '';
        console.log('由于选了第三个，所以tableList里面group和ueser没值！看后端之后怎么操作工单池流转');
      } else if (tableList.orderHandler && this.showFlowType) {
        const handler = tableList.orderHandler.value;
        console.log('handler', handler);
        tableList.handleUser = cutString(handler, '_');
        const [handleGroup] = getFatherBySon(handler, this.handleList);
        tableList.handleGroup = cutString(handleGroup.value, '_');
        delete tableList.orderHandler;

        console.log('qweqwe', handler, tableList, handleGroup);
      }

      const filteredArr = this.nowCategoryID;

      console.log(tableList);
      // try {
      let [result, err] = await this.addOrderFn(tableList);
      console.log('看下结果1', result, err, commitFlag);
      if (result?.code == '10000') {
        if (commitFlag) {
          this.$notification.open({
            message: '成功',
            description: '内容编辑成功',
            icon: <a-icon type="smile" style="color: #108ee9" />,
          });
        } else {
          this.$success({
            title: result.data,
            okText: '我知道了',
          });
        }
      } else if (err?.code == '60000') {
        console.log(commitFlag, '---commitFlag');
        if (commitFlag) {
          this.$notification.open({
            message: '创建失败',
            description: '工单内容操作失败',
            icon: <a-icon type="exclamation" />,
          });
        } else {
          this.showFailModal = true;
          this.failMsg = err.message;
          this.failOrderId = err.subCode;
        }
      }
      // } catch (err) {
      //   this.$notification.open({
      //     message: '失败',
      //     description: '内容编辑失败',
      //     icon: <a-icon type="exclamation" />,
      //   });
      // }

      this.showMod = false; //提交后变回去
      this.choiceValue = undefined; //提交后变回去
      this.showGuest = false; //提交后变回去
      this.orderTagList = [];
      this.orderTags = [];
      this.orderName = [];
      this.exValues = []; //去掉模板里面的值
      this.exList = [
        {
          fields: [{}],
        },
      ];
      this.rules = {};
      this.contentInput = '';
      this.visitorSex = 0;

      this.nowTab = 1;
      this.man = false;
      this.lady = false;
      this.backName = '';
      this.exValues = [];
      this.leftChange(1);
    },
    async modalCancelHandler() {
      if (this.nowCategoryID) {
        //只有选择分类之后才能获取到orderId
        console.log('参数', this.nowCategoryID, this.leftChoice);
        let [result] = await orderDraftAPI(this.nowCategoryID, this.leftChoice);
        console.log('取消草稿', result);
      }

      this.showMod = false; //提交后变回去
      this.choiceValue = undefined; //提交后变回去
      this.showGuest = false; //提交后变回去
      this.orderTagList = [];
      this.orderTags = [];
      this.orderName = [];
      this.exList = [
        {
          fields: [{}],
        },
      ];
      this.rules = {};
      this.contentInput = '';
      this.visitorSex = 0;
      this.showFlowType = false;
      this.showTemp = false;
      this.man = false;
      this.lady = false;
      this.backName = '';
      this.exValues = [];
      console.log('取消按钮');
    },
    deleteRowHandler() {},
    rowAdd() {
      this.$refs.crud.switchModalView(true, 'ADD');
    },
    async listAdd() {
      this.currentPage = 1;
      this.stationOptions = [];
      this.getStationOptions();
      if (this.createFlag == 0 || this.createFlag == 1) {
        this.stationRequired = false;
        // this.handlegroups.value = undefined;
        // this.handleusers.value = undefined;
        this.$refs.crud.switchModalView(true, 'ADD', {
          phoneNumber: this.customerNumber,
          orderStatus: undefined,
          workTypeId: undefined,
          urgency: '3',
          visitorName: undefined,
          visitorPhone: undefined,
          orderStatus: '7',
          orderTemplateId: undefined,
          userName: undefined,
          city: undefined,
          stationId: undefined,
          vehicleBrand: undefined,
          vehicleModel: undefined,
          vehicleYear: undefined,
          maintenanceUser: undefined,
        });
        // this.$refs.crud.setFormFields({ phoneNumber: this.customerNumber });
      } else {
        let [res] = await editOrder('', this.leftChoice);
        this.$refs.addOrder.rowAdd(res.data, true);
      }
      console.log('紧急程度', this.createFlag);
    },
    guestTagHandler() {
      console.log('增加客户标签');
    },
    callbackContent(key) {
      this.key = key;
      console.log('你好', key);
      this.loadData();
    }, //标签页切换
    spell(name, gen) {
      if (gen == 1) {
        //点击先生时候
        if (!this.man) {
          //初次点击，以及切换
          this.visitorName = name + '先生';
          this.lady = false;
          this.man = !this.man;
          this.visitorSex = 1;
        } else if (this.man) {
          //重复点击同一性别，也就是取消
          this.visitorName = name;
          this.man = !this.man;
          this.visitorSex = 0;
        }
      } else if (gen == 2) {
        if (!this.lady) {
          this.visitorName = name + '女士';
          this.man = false;
          this.lady = !this.lady;
          this.visitorSex = 2;
        } else if (this.lady) {
          this.visitorName = name;
          this.lady = !this.lady;
          this.visitorSex = 0;
        }
      } else if (gen == 3) {
        //gen == 3代表不选，我就点着玩
        if (this.lady) {
          this.visitorName = name + '女士';
          this.visitorSex = 2;
        } else if (this.man) {
          this.visitorName = name + '先生';
          this.visitorSex = 1;
        }
      }
      console.log('现在visitorSex' + this.visitorSex);
      console.log(this.visitorName);
    },
    nameFn(name) {
      this.tableList.visitorName = name;
      if (this.man || this.lady) {
        // 在有性别选择了的时候才会执行
        this.spell(name, 3);
      }
      console.log(this.tableList.visitorName);
    },
    showGuestfn(params) {
      this.visitorName = params.visitorName;
      this.visitorPhone = params.visitorPhone;
      this.$refs.crud.setFormFields({
        visitorName: params.visitorName,
        visitorPhone: params.visitorPhone,
      });
      this.showGuest = !this.showGuest;
      this.visitorRules = {
        visitorName: [{ required: this.showGuest, message: '请填写访客名称', trigger: 'blur' }],
        visitorPhone: [
          {
            required: this.showGuest,
            message: '输入电话号码格式不正确',
            pattern: /^\d+$/,
            trigger: 'blur',
          },
          {
            validator: async (rule, value, callback) => {
              if ((params.visitorInfoId || params.commitFlag === '0') && value === params.visitorPhone) {
                return callback();
              } else {
                const res = await checkPhone(value);
                if (res[0] === undefined) {
                  callback('电话号码已被占用');
                } else {
                  callback();
                }
              }
            },
            trigger: 'blur',
          },
        ],
      };
    },

    //获取工单模板，触发事件在选择工单分类之后
    async getMod() {
      // this.workType = workType.data;
      // console.log(this.workType); //获取分类

      let [result] = await listAPI(this.nowCategoryID);
      this.exList = result.data; //获取模板
      console.log('看看exList', this.exList);
      let choiceExList = this.exList[this.NumberChoice];
      try {
        //这里为了防止收到空报错
        for (const item of choiceExList.fields || []) {
          //让模版输入框获得初始值
          this.rules[item.templateFieldId] = [{ required: item.emptyFlag === '0' ? true : false, message: '请输入' }];
          this.exValues.push(item.defaultValue || undefined);
        }
        this.templateId = this.exList[0].templateId;
        this.choiceValue = this.exList[0].templateName; //获取分类的时候给予模板一个默认值
        // console.log(this.exList[this.NumberChoice].fields);
      } catch (err) {
        console.log('这个分类下没有模板');
      }
      console.log('看看exList', this.exList);
    },

    //工单模板选择第几个
    async handleChange(value) {
      this.showMod = true;
      this.rules = {};
      console.log('选择下标' + value);
      this.modChoice = value;

      let choiceExList = this.exList[this.NumberChoice];
      try {
        for (const item of choiceExList.fields || []) {
          this.rules[item.templateFieldId] = [{ required: item.emptyFlag === '0' ? true : false, message: '请输入' }];
          //让模版输入框获得初始值
          if (item !== undefined) this.exValues.push(item.defaultValue || null);
        }
        this.templateId = this.exList[value].templateId;

        this.$refs.crud.setFormFields({ exValues: this.exValues });

        console.log('模板ID数组prop', this.rules);
      } catch (err) {
        console.log('这个分类下没有模板');
      }
    },

    //tag子组件
    async transTags(value) {
      this.orderName = [];
      this.orderTags = [];
      this.orderTags = value;
      console.log(value);
      let result = await DictCodeAPI('order_tag');
      console.log(result);
      try {
        this.orderName = value.map((value) => {
          const item = result.find((obj) => obj.value == value);
          return item ? item.label : null;
        });
        console.log(this.orderName);
      } catch (err) {
        console.log('看起来你没输入tag');
      }
    },
    changeAddTags(value) {
      this.showTags = !value;
    },

    //选择无模板时候，因为没有内容会报错，但是为了不改变之前的结构，选择捕获错误，顺便改showMod为false
    NoWrongHere() {
      try {
        console.log('你好，我报了个错');
      } catch {
        console.log('然后我不想改了');
      } finally {
        this.showMod = false;
      }
    },
    //获取工单分类
    async workTypeFn() {
      if (this.isWorkTypeFn == false) {
        let [result] = await workTypeAPI({});
        if (result.code === '10000') {
          this.workType = result.data;
          console.log(result.data);
        }
        this.isWorkTypeFn = true;
      }
    },

    //根据工单分类获取默认流转方式
    async flowTypeFn() {
      if (!this.nowCategoryID) return;
      let [result] = await flowTypeAPI(this.nowCategoryID);
      if (result.code === '10000') {
        console.log(result);
        //是否显示后两个选项
        console.log('显示1，不显示0，你是：' + result.data.enabledFlag);
        this.enabledFlag = result.data.enabledFlag;
        console.log('0就人工流转，1就按fowType，你是：' + result.data.autoFlag);
        console.log('自动流转flowType是多少', result.data.flowType);

        //默认流转方式，并且会在获取到默认流转方式时候发送一个请求用于获取受理组、受理人数据
        let autoFlag = result.data.autoFlag;
        if (autoFlag == '0') {
          this.flowRadio = '1';
          this.autoDisable = false;
          // let result = await userListAPI();
          // this.handleList = result;
          console.log(this.handleList);
        } else if (autoFlag == '1') {
          this.selectFlowType(result.data.flowType);
        }
      }
    },
    async selectFlowType(flowType) {
      this.flowRadio = flowType;
      if (!this.nowCategoryID) return;
      let [result] = await getAutoAllocationAPI(this.nowCategoryID);
      console.log(result);

      if (this.flowRadio == '2') {
        //没有受理的情况下
        if (JSON.stringify(result.data) == '{}') {
          console.log('==没有==');
          this.autoFlag = true;
          this.flowRadio = '1';
          let test = { target: { value: '1' } };
          this.choiceManFlow(test);
        } else {
          this.autoDisable = true;
          this.autoFlag = false;
          const [autoManGroup] = this.handleList.filter((item) => item.value.includes(result.data.handleGroupId));
          const [autoManInline] = autoManGroup.children.filter((item) => item.value.includes(result.data.handleUserId));
          if (autoManInline.userStatus === '3') {
            this.flowRadio = '3';
            this.autoFlag = true;
          } else {
            this.$refs.crud.setFormFields({
              orderHandler: { value: result.data.handleUserId + '_' + result.data.handleUserName },
            });
          }
          //再备份一个，避免后续选择radio获取不到值
          this.backupHandle.userlabel = result.data.handleUserName;
          this.backupHandle.userValue = result.data.handleUserId;
          this.backupHandle.grouplabel = result.data.handleGroupName;
          this.backupHandle.groupValue = result.data.handleGroupId;

          // //自动分配离线再分配
          // if (autoManInline.userStatus == '3') {
          //   this.flowRadio = '3';
          // } else {
          //   this.$refs.crud.setFormFields({ orderHandler: { value: handlerId } });
          // }
        }
      } else if (this.flowRadio == '3') {
        console.log('默认选择了工单池分配！请给我请求');
      } else if (this.flowRadio == '1') {
        this.autoDisable = false;
        // let result = await userListAPI();
        // this.handleList = result;
        console.log(result);
        console.log('这里autoFlag为1之后，flowType为1，自动选择了人工流转');
      }
      console.log('按照传入参数选择flowType，还没写完逻辑，值为：' + result.data.flowType);

      console.log('这个时候是自动分配了一个受理人和受理组');
    },
    //人工流转，自动分配，工单池分配三个选择后触发事件
    async choiceManFlow({ target }) {
      //选择后需要清空受理组和受理人，避免左右横跳出错值
      // this.handleList = [];
      this.handleUserList = [];
      if (target.value == '1') {
        this.autoDisable = false;
        // this.handleusers = { label: '', value: undefined }; //当前处理人
        // this.handlegroups = { label: '', value: undefined }; //当前处理组
        // let result = await userListAPI();
        // this.handleList = result;
        console.log('人工流转列表是', this.handleList);
      } else if (target.value == '2') {
        this.autoDisable = true;
        console.log(this.backupHandle);
        this.selectFlowType('2');
      } else if (target.value == '3') {
        this.autoDisable = false;
        console.log('藏起来吧！');
      }
    },
    // clearUserList() {
    //   this.handleusers.label = ''; //切换受理组的时候需要清空受理人，避免选一个受理人后保留到选择另一个受理组
    //   this.handleusers.value = undefined;
    // },
    //没有自动分配人员的时候会触发事件，自动跳到工单池流转，并且弹出提示。这个第一次自动和后面选择都需要用到
    // noAutoMan(ids) {
    //   if (ids.groupValue == undefined || ids.userValue == undefined || ids.groupValue == "" || ids.userValue == "") {
    //     this.flowRadio = '1';
    //     let test = {
    //       target: {
    //         value: '1',
    //       },
    //     };
    //     this.choiceManFlow(test);
    //   }
    // },
    // async addOrderFn(params) {
    //   let [result] = await addAPI(params);
    //   return result;
    // },
    async addOrderFn(params) {
      let [result, err] = await addAPI(params);
      console.log('---------err', err);
      return [result, err];
    },

    //针对人工选择工单时，选择工单受理组后，显示工单受理人
    choiceHandleGroup(value) {
      this.handleUserList = value;
      console.log(this.handleUserList);
    },
    //左侧通话记录接口
    async leftChange(value) {
      console.log(value);
      let params = {
        flag: value,
        pageNum: 1,
        pageSize: this.leftSize,
        customerNumber: this.inputTel,
      };
      this.leftPage = 1;
      this.flag = value;
      let [result] = await callListAPI(params);
      if (result.code === '10000') {
        this.phoneList = result.data;
        this.leftPageCount = result.count;
      }
      console.log('phoneList', this.phoneList);
      try {
        this.choiceOne(
          this.phoneList[0].mainUniqueId,
          this.phoneList[0].customerNumber,
          this.phoneList[0].customerProvince,
          this.phoneList[0].customerCity,
          0
        );
      } catch (err) {
        console.log('翻tab加载第一个出错了');
      }
    },

    async leftPageChange(value) {
      console.log(value);
      let params = {
        flag: this.flag,
        pageNum: value,
        pageSize: this.leftSize,
        customerNumber: this.inputTel,
      };
      let [result] = await callListAPI(params);
      if (result.code === '10000') {
        this.phoneList = result.data;
      }
      console.log(this.phoneList);
    },

    //选择左侧其中一个
    async choiceOne(mainUniqueId, phone, address, city, index) {
      this.leftChoice = mainUniqueId;
      this.customerNumber = phone;
      this.customerProvince = address;
      this.customerCity = city;
      try {
        this.createFlag = this.phoneList[index].createOrderFlag;
      } catch (err) {
        console.log('这个没有createOrderFlag');
      }
      console.log('选择了flag', this.createFlag);

      this.detailRight = {}; //每次都会清空
      this.firstFont = '';
      this.tagName = [];
      this.fromGrade = '';
      this.fromName = '';
      this.defaultAddress = '';
      this.backName = '';
      let [result] = await detailAPI({ visitorPhone: phone });

      console.log('你选择的通话记录', result);
      try {
        this.firstFont = result.data.visitorName[0];
        this.detailRight = result.data;
        console.log(this.detailRight); //传入正常详情数据，下面开始特殊化
        this.detailRight.lowAddress = city; ////小地址，头部展示江苏无锡
        this.defaultAddress = result.data.address; //前面的地址
        this.noDotAddress = result.data.address.replace(/,/g, '');
        this.detailRight.address = this.detailRight.address.split(','); //地址变数组

        this.visitorSex = result.data.visitorSex; //访客详情性别用得上
        this.backName = result.data.visitorName.replace(/先生|女士/g, '');
      } catch (err) {
        console.log('名字有问题，或者地址有问题');
      }
      try {
        let tags = await DictCodeAPI('visitor_tag'); //标签字典部分
        console.log('tags', tags);
        this.tagName = valueFindFn(tags, result.data.visitorTag).split(',');
        console.log(this.tagName);
      } catch (err) {
        console.log('右侧显示是没有标签的');
      }

      try {
        let froms = await DictCodeAPI('visitor_from'); //访客来源字典部分
        console.log('来源的字典', froms);
        let from = froms.find((item) => item.value === result.data.visitorFrom);
        this.fromName = from.label;
        console.log(this.fromName);
      } catch (err) {
        console.log('右侧显示是没有来源的');
      }

      try {
        let levels = await DictCodeAPI('visitor_grade'); //访客来源字典部分
        console.log('来源的字典', levels);
        let level = levels.find((item) => item.value === result.data.visitorGrade);
        this.fromGrade = level.label;
        console.log(this.fromGrade);
      } catch (err) {
        console.log('右侧显示是没有等级的');
      }
      this.showRight = true; //展示右侧content区域，顺便解放创建工单按钮
      this.isRightWrite = false; //关闭右侧编辑状态，不知道后续耦合度如何

      this.loadData(); //自动执行获取第一次历史工单信息

      //新需求，工单数组
      console.log('选择的phoneList', result.data);
      console.log('index是否正确', index);
      try {
        this.stateOrderId(this.phoneList[index].orderInfos);
      } catch (err) {
        console.log('没有orderInfos');
      }
    },
    //左侧选择一个，要把orderId数组传出去，记得先清空
    async stateOrderId(info) {
      let arr = [];
      let arrStatus = [];
      let arrTime = [];
      console.log('没处理数组', info);
      try {
        for (const item of info) {
          console.log('数组', item);
          arr.push(item.orderId);
          arrStatus.push(item.orderStatus);
          arrTime.push(item.operationTime);
        }
        console.log('新添加的info的ID数组', arr);
        await this.$store.dispatch('base/orderIdFn', arr);
        await this.$store.dispatch('base/statusFn', arrStatus);
        await this.$store.dispatch('base/timeFn', arrTime);
        console.log('storeOrderId', this.$store.state.base.orderId);
        console.log('orderId的状态数组', this.$store.state.base.arrStatus);
      } catch (err) {
        console.log('这个里面没有orderId数组');
        this.$store.dispatch('base/orderIdFn', []);
        await this.$store.dispatch('base/statusFn', []);
        await this.$store.dispatch('base/timeFn', []);
      }
    },
    //省份表获取
    getProvince() {
      // let str = province.toString();
      // let obj = Object.assign({}, str);
      let obj = JSON.parse(JSON.stringify(province));
      console.log(obj);
      console.log(Object.entries(obj)); //测试
      const result = Object.entries(obj).map(([province, cities]) => ({
        province: province,
        cities: Object.entries(cities).map(([city, districts]) => ({
          city,
          districts,
        })),
      }));
      console.log(result);
      this.provinceList = this.transformData(result);
      console.log(this.provinceList);
    },
    transformData(data) {
      //把数据转换成级联可识别样式
      return data.map((province) => {
        return {
          label: province.province,
          value: province.province,
          children: province.cities.map((city) => {
            return {
              label: city.city,
              value: city.city,
              children: city.districts.map((district) => {
                return {
                  label: district,
                  value: district,
                };
              }),
            };
          }),
        };
      });
    },

    //右侧编辑按钮
    rightWrite() {
      this.isRightWrite = !this.isRightWrite;
      if (this.isRightWrite) {
        console.log('编辑模式');
        this.detailRight.visitorPhone = this.customerNumber; //获取默认电话
        DictCodeAPI('visitor_grade').then((res) => {
          this.visitorGradeSelect = res;
          console.log(this.visitorGradeSelect);
        });
        DictCodeAPI('visitor_from').then((res) => {
          this.visitorFromSelect = res;
          console.log(this.visitorFromSelect);
        });
        DictCodeAPI('visitor_tag').then((res) => {
          this.tagSelect = res;
          console.log(this.tagSelect);
        });
      } else {
        this.choiceOne(this.mainUniqueId, this.customerNumber, this.customerProvince, this.customerCity);
        //对于监听不到对象内属性，直接刷新重新获取
      }
    },

    //点击确定上传content编辑的数据
    async upLoadDetail() {
      console.log('保存?', this.detailRight);
      let newList = this.detailRight;
      try {
        if (this.detailRight.address) {
          newList.address = this.detailRight.address.join(',');
        } else {
          newList.address = '';
        }
        newList.visitorFrom = labelFindFn(this.visitorFromSelect, this.fromName);
        newList.visitorGrade = labelFindFn(this.visitorGradeSelect, this.fromGrade);
        newList.visitorTag = labelFindFn(this.tagSelect, this.tagName.join(','));
        newList.visitorSex = this.visitorSex;
        if (this.visitorSex == '1') {
          newList.visitorName = this.backName + '先生';
        } else if (this.visitorSex == '2') {
          newList.visitorName = this.backName + '女士';
        } else if (this.visitorSex == '0') {
          newList.visitorName = this.backName;
        }
      } catch (err) {
        console.error(err);
      }
      console.log(this.detailRight);
      console.log('传的数据呢', newList);

      let [result] = await editVisitorAPI(newList);
      console.log('修改', result);
      if (result.code == '10000') {
        this.$notification.open({
          message: '操作成功',
          description: '内容编辑成功',
          icon: <a-icon type="smile" style="color: #108ee9" />,
        });
      } else {
        this.$notification.open({
          message: '操作失败',
          description: '内容编辑失败',
          icon: <a-icon type="exclamation" />,
        });
      }

      this.tagSelect = [];
      this.visitorFromSelect = [];
      this.visitorGradeSelect = []; //提交后把这几个数组置空，避免意外发生

      this.choiceOne(this.mainUniqueId, this.customerNumber, this.customerProvince, this.customerCity);
    },
    tagTest() {
      console.log(this.tagSelect);
      console.log(this.tagName);
    },

    //紧急弹窗确认
    modalOK() {
      this.prevFlow = this.nowFlow; //获取备份flowRadio，用于紧急时弹窗提示
      console.log('上一次的已保存', this.prevFlow);
      this.modalSeen = false;
    },
    modalCancel() {
      this.$refs.crud.setFormFields({ urgency: this.prevFlow });
      this.modalSeen = false;
    },
    async historyList() {
      console.log('选择了历史工单');
    },
    fieldSelect(value) {
      console.log('模板内多选', value);
    },
    fieldCount(value) {
      console.log('模板内数字');
    },
    selectChange(value) {
      console.log(value);
    },
    addressChange(value) {
      this.detailRight.address = value;
      console.log('看看修改的地址', value);
    },
    async getModSelect(key) {
      let result = await defaultDict(key);
      console.log(result);
      this.selectOne = result;
    },
    async getModSecSelect(key) {
      let result = await defaultDict(key);
      console.log(result);
      this.selectCom = result;
    },
    //点击工单号跳转到
    toOrderDetail(row) {
      this.$router.push({
        path: '/workorderdetail',
        query: {
          orderId: row.orderId,
        },
      });
    },
    // 选择预设回复模板
    handleSelect(value) {
      this.contentInput += value.content;
      this.$refs.crud.setFormFields({ orderContent: this.contentInput });
    },
    giveParam(value) {
      this.contentInput = value;
    },

    tryIt(params) {
      console.log('能不能拿到值', params);
    },
  },

  watch: {
    watchStatus(newValue, oldValue) {
      if (newValue) {
        //客服呼出电话时输入的客户号码
        if (this.$store.state.base.callNumber) {
          this.customerNumber = this.$store.state.base.callNumber;
        }
        this.$message.info('打电话过来了');
        this.leftChange(1);
        this.choiceOne(this.leftChoice, this.customerNumber, this.customerProvince, this.customerCity);
        this.$store.dispatch('base/isCallFn', false);
        //将呼出的客户号码置空
        this.$store.dispatch('base/callNumberFn', '');
      }
    },
    choiceExList: {
      handler(n) {
        console.log(n);
        // p
      },
      deep: true,
    },
  },
};
