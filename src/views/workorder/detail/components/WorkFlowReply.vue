<template>
  <a-spin :spinning="spin">
    <!-- 工单标题 -->
    <a-row type="flex" justify="space-between" class="workTitle">
      <a-col :span="8" :offset="1">
        <a-row>
          <h1>{{ workDetail.categoryName }}</h1>
        </a-row>
        <a-row>
          <p>
            <a-space>
              <span>提交人：{{ workDetail.createByName }}</span>
              <span style="margin-left: 16px">提交时间：{{ workDetail.createTime }}</span>
            </a-space>
          </p>
        </a-row>
      </a-col>
      <a-col :span="6" class="btnGroup">
        <a-space>
          <a-button
            type="primary"
            icon="smile"
            v-if="isHandled"
            v-hasPermi="['system:workorder:orderHandle']"
            @click="takeOrder"
          >
            接单
          </a-button>
          <a-button type="primary" icon="bell" v-hasPermi="['system:workorder:orderRemind']" @click="remindOrder">
            催办
          </a-button>
          <a-button @click="handleBack" icon="rollback">返回</a-button>
          <!-- <a-button
            type="disable"
            icon="delete"
            v-hasPermi="['system:workorder:orderDrop']"
            v-if="workDetail.orderStatus === '9' ? false : true"
            @click="dropOrder"
          >
            废弃
          </a-button> -->
        </a-space>
      </a-col>
    </a-row>
    <!-- 工单详情 -->
    <a-row style="font-size: 12px">
      <a-col :span="20" :offset="1" class="topGap">
        <a-row class="deRow" type="flex" justify="space-between">
          <a-col :span="8"
            >工单编号：<span class="deItem">{{ workDetail.orderId }}</span></a-col
          >
          <a-col :span="8" style="padding-left: 16px"
            >优先级：
            <span class="deItem" :style="{ color: urgencyColor(workDetail.urgency) }">{{
              workDetail.urgency
            }}</span></a-col
          >
          <a-col :span="8"
            >流转方式： <span class="deItem">{{ workDetail.flowType ? workDetail.flowType : '未流转' }}</span></a-col
          >
        </a-row>
        <a-row class="deRow" type="flex">
          <a-col :span="8"
            >工单类别：<span class="deItem">{{ workDetail.categoryName }}</span></a-col
          >
          <a-col :span="8" style="padding-left: 16px"
            >工单来源：<span class="deItem">{{ workDetail.orderSource }}</span></a-col
          >
          <a-col :span="8" v-if="merchant.merchantId == '40002'"
            >所在城市：<span class="deItem">{{ workDetail.city }}</span></a-col
          >
          <!-- <a-col :span="8"
            >工单手机号： <span class="deItem">{{ workDetail.phoneNumber }}</span></a-col
          > -->
        </a-row>
        <a-row class="deRow" type="flex">
          <a-col :span="8" v-if="merchant.merchantId == '40002'"
            >用户姓名： <span class="deItem">{{ workDetail.userName }}</span></a-col
          >
          <a-col :span="8" v-if="merchant.merchantId == '40002'"
            >用户手机号： <span class="deItem">{{ workDetail.phoneNumber }}</span></a-col
          >
          <a-col :span="8" v-if="merchant.merchantId == '40002'"
            >车型品牌：
            <span class="deItem">{{
              (workDetail.vehicleBrand || '') +
              '-' +
              (workDetail.vehicleModel || '') +
              '-' +
              (workDetail.vehicleYear || '')
            }}</span></a-col
          >
        </a-row>
        <a-row class="deRow" type="flex" v-if="merchant.merchantId == '40002'">
          <a-col :span="8"
            >站点名称： <span class="deItem">{{ workDetail.stationName }}</span></a-col
          >
        </a-row>
        <a-row class="deRow" @click="clickImg" style="display: flex">
          <span style="vertical-align: top">问题描述：</span>
          <a-col :span="18">
            <ShowEditor :value="workDetail.orderContent" :disabled="true" />
            <ShowUploads :attachment-list="reShowFile" />
          </a-col>
        </a-row>
        <a-row class="deRow" v-if="workDetail.handleUserName">
          <a-col :span="20"
            >当前处理人： <span class="deItem">{{ workDetail.handleUserName }}</span></a-col
          >
        </a-row>
        <a-row class="deRow" v-if="thisTags && thisTags.length > 0">
          <a-col :span="20"
            >工单标签：
            <a-tag class="tags" color="pink" v-for="(item, index) in thisTags" :key="index">{{
              item.label
            }}</a-tag></a-col
          >
        </a-row>
        <a-row v-if="addFields">
          <a-col>
            <div class="templateMark">
              <a-icon type="profile" class="myIcon" theme="twoTone" two-tone-color="#63a0bb" />自定义模板
            </div>
          </a-col>
        </a-row>
        <a-row class="deRow">
          <template v-for="(item, index) in addFields">
            <a-col :span="item.fieldType === '4' ? 20 : 8" :key="index">
              <FieldTemplate :fieldInfo="item" />
            </a-col>
          </template>
        </a-row>
        <a-row :gutter="[8, 16]">
          <a-col>
            <div class="nextMark">
              <a-icon type="control" class="myIcon" theme="twoTone" two-tone-color="#7d63bb" />工单状态与转派
            </div>
          </a-col>
        </a-row>
        <a-row class="deRow">
          <a-col :span="8">
            <div>
              工单状态：
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                style="width: 240px"
                v-model="workDetail.orderStatus"
              >
                <a-select-option v-for="item in orderStatus" :key="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :span="8" v-if="workDetail.orderStatus == '1' || workDetail.orderStatus == '11'">
            工单转派：
            <a-tree-select
              placeholder="请选择转派人"
              v-model="nextFlower"
              :treeData="userList"
              showSearch
              labelInValue
              class="nextFlower"
              :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            >
              <template slot="myTitle" slot-scope="item">
                <StatuNode :newNodes="item" />
              </template>
            </a-tree-select>
          </a-col>
          <a-col
            :span="8"
            v-if="merchant.merchantId == '40002' && (workDetail.orderStatus == '1' || workDetail.orderStatus == '11')"
          >
            运维受理人：
            <a-select placeholder="请选择运维受理人" v-model="maintenanceUser" style="width: 240px">
              <a-select-option v-for="item in maintenanceOptions" :key="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-col>
        </a-row>
      </a-col>
    </a-row>
    <!-- 回复框 -->
    <a-row :gutter="[8, 16]">
      <a-col :span="20" :offset="1">
        <div class="replyMark">
          <a-icon type="message" theme="twoTone" two-tone-color="#63bb8b" class="myIcon" />工单回复
        </div>
      </a-col>
    </a-row>
    <a-row class="reRow">
      <a-col :span="18" :offset="1">
        <AddReply :orderSort="workDetail.categoryId" @selectedReply="handleSelect" />
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="20" :offset="1">
        <ProductEditor :value="replyDraft" :addFileList="addFileList" @input="handleInput" @addFile="addFile" />
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="4" :offset="18">
        <a-space>
          <a-button @click="handleOrder(1)">保存草稿</a-button>
          <a-button @click="handleOrder(2)" v-hasPermi="['system:workorder:submitOrder']" type="primary">提交</a-button>
        </a-space>
      </a-col>
    </a-row>
    <ImgViewer :show="show" :imgView="imgView" @cancelModal="cancelModal" />
  </a-spin>
</template>

<script setup>
import AddReply from '@/components/Editor/AddReply.vue';
import ImgViewer from '@/components/ImgModal/ImgViewer';
import ProductEditor from '@/components/Editor/ProductEditor.vue';
import ShowEditor from '@/components/Editor/ShowEditor.vue';
import { computed, onMounted, ref, watchEffect } from 'vue';
import { message } from 'ant-design-vue';
import { orderRemind, orderTake, orderDrop, orderHandle, orderSaveDraft } from '@/api/system/workorder';
import { maintenanceList } from '@/api/system/callBench';
import {
  cutString,
  defaultDict,
  defaultMsg,
  getFatherBySon,
  setCustomSearchTree,
  urgencyColor,
} from '@/utils/system/common';
import store from '@/store';
import FieldTemplate from '@/components/AddField/FieldTemplate.vue';
import { useRouter } from 'vue-router/composables';
import StatuNode from '@/components/TreeNode/StatuNode.vue';
import ShowUploads from '@/components/UploadImg/ShowUploads.vue';
const props = defineProps({
  workDetail: Object,
  addFields: Array,
});

let orderStatus = ref();
let nextFlower = ref();
let userList = ref();
let workDetail = ref(props.workDetail);
let isHandled = ref(props.workDetail.handleUser ? false : true);
let replyDraft = ref(props.workDetail.replyContent);
let attachment = ref(props.workDetail.replyAttachment); // 工单回复附件列表
let addFileList = ref(); // 工单回复保存的附件
let reShowFile = ref(); // 创建工单上传的附件
let spin = ref(false);
let show = ref(false);
let imgView = ref();
let orderTags = ref([]);
let thisTags = ref([]);
let maintenanceOptions = ref([]);
let maintenanceUser = ref();
const route = useRouter();
const emits = defineEmits(['submitFinish']);
const merchant = computed(() => store.state.base.merchant || {});
// 把富文本的字段放置在最后
let addFields = computed(() => {
  if (!props.addFields || props.addFields === null) return null;
  try {
    if (!props.addFields[0].value) {
      return null;
    }
  } catch {
    return null;
  }
  let final = props.addFields;
  let res = [];
  final.forEach((item, index) => {
    if (item.fieldType === '4') {
      res.push(index);
    }
  });
  res.map((item) => {
    const target = final[item];
    const len = final.length;
    final.splice(item, 1);
    final.splice(len, 0, target);
  });
  return final;
});
function handleBack() {
  route.back();
}
// 点击图片放大
function clickImg(e) {
  if (e.target.src) {
    imgView.value = e.target.src;
    show.value = true;
  }
}
function cancelModal() {
  show.value = false;
  imgView.value = '';
}
// 接单
async function takeOrder() {
  if (workDetail.value.isTake) {
    const [result] = await orderTake({ orderId: workDetail.value.orderId });
    defaultMsg(result, '接单');
    if (result.code === '10000') {
      isHandled.value = false;
    }
  } else {
    message.error('很抱歉，您无权接单～');
  }
}
// 催单
async function remindOrder() {
  const [result, err] = await orderRemind({ orderId: workDetail.value.orderId });
  defaultMsg(result, '催单', err);
}
// 废弃工单
// async function dropOrder() {
//   if (workDetail.value.isDrop) {
//     const [result] = await orderDrop({ orderId: workDetail.value.orderId });
//     defaultMsg(result, '废弃');
//     if (result.code === '10000') {
//       route.push({
//         path: '/workorderbench',
//         query: {
//           handleStatu: '6',
//         },
//       });
//     }
//   } else {
//     message.error('很抱歉，您无权废弃该工单～');
//   }
// }
function handleInput(param) {
  replyDraft.value = param;
}
function addFile(param) {
  if (typeof param === String) {
    param = JSON.parse(JSON.stringify(param));
  }
  param = param.map((item) => ({
    uid: item.uid,
    url: item.url,
    name: item.name,
  }));
  addFileList.value = param;
}
// 选择预设回复模板
function handleSelect(value) {
  replyDraft.value += value.content;
}
// 提交工单
async function handleOrder(submitType) {
  spin.value = true;
  let params = {
    orderId: workDetail.value.orderId,
    replyStatus: submitType,
    replyContent: replyDraft.value ? replyDraft.value : '',
    orderStatus: workDetail.value.orderStatus,
    orderReplyId: workDetail.value.orderReplyId,
    replyAttachmentList: addFileList.value,
  };
  let msg = '保存';
  if (submitType === 2) {
    msg = '提交';
    if (nextFlower.value) {
      const handler = nextFlower.value.value;
      const [father] = getFatherBySon(handler, userList.value);
      params.handleGroup = cutString(father.value, '_');
      params.handleUser = cutString(handler, '_');
    }
    if (maintenanceUser.value) {
      params.maintenanceUser = maintenanceUser.value;
      params.maintenanceUserName = maintenanceOptions.value?.find((x) => x.value == maintenanceUser.value)?.nickName;
    }
    try {
      const [result] = await orderHandle(params);
      spin.value = false;
      defaultMsg(result, msg);
      if (result.code === '10000') {
        route.push({
          path: '/workorderbench',
          query: {
            handleStatu: '6',
          },
        });
      }
    } catch (err) {
      console.log(err);
    }
  } else {
    const [res] = await orderSaveDraft(params);
    spin.value = false;
    defaultMsg(res, msg);
    submitFinish();
  }
}
function submitFinish() {
  emits('submitFinish');
}
//获取运维人员列表
async function getMaintenanceOptions() {
  const [res] = await maintenanceList({});
  maintenanceOptions.value = res?.data?.map((x) => {
    return { ...x, label: x.userName + '-' + x.nickName, value: x.userId };
  });
}
onMounted(() => {
  store.dispatch('base/GetUserList', { nickName: '' }).then((result) => {
    userList.value = setCustomSearchTree(result);
  });
  // defaultDict('order_status').then((res) => {
  //   orderStatus.value = res.filter((item) => item.value !== '0');
  // });
  orderStatus.value = [
    { label: '处理中', value: '1' },
    { label: '处理中（重新处理）', value: '11' },
    { label: '待回访', value: '2' },
    { label: '无需处理', value: '5' },
    { label: '已完结', value: '7' },
  ];
  defaultDict('order_tag').then((res) => {
    orderTags.value = res;
  });
  getMaintenanceOptions();
});
watchEffect(() => {
  workDetail.value = props.workDetail;
  replyDraft.value = props.workDetail.replyContent;
  isHandled.value = props.workDetail.handleUser ? false : true;
  attachment.value = props.workDetail.replyAttachment;
  if (attachment.value) {
    try {
      addFileList.value = JSON.parse(attachment.value);
    } catch {
      console.log('无附件');
    }
  }
  if (props.workDetail.attachment) {
    try {
      reShowFile.value = JSON.parse(props.workDetail.attachment);
    } catch {
      console.log('无回显附件');
    }
  }
  if (props.workDetail.orderTag) {
    const myTag = props.workDetail.orderTag.split(',');
    thisTags.value = myTag.map((tag) => {
      const [result] = orderTags.value.filter((item) => item.value === tag);
      return result;
    });
  }
});
</script>
<style lang="less" scoped>
.workTitle {
  h1 {
    font-size: 32px;
  }
  .btnGroup {
    display: flex;
    align-items: center;
  }
}
/deep/ .ant-col {
  color: #a9a5a5;
}
/deep/ .ql-editor {
  color: #0d0c0c;
}
.topGap {
  border-top: 1px solid #ccc;
  padding-top: 8px;
  .deRow {
    margin: 16px 32px;
    .fieldItem {
      margin: 8px 4px;
    }
  }
  .deItem {
    color: #0d0c0c;
  }
}
.reRow {
  margin: 8px 0;
  font-size: 13px;
  /deep/ .replyLine {
    margin-left: 32px;
  }
}
.orderContent {
  display: inline-block;
  border: 1px solid #cccccc;
  border-radius: 4px;
  padding: 8px;
  img {
    width: 40%;
  }
}
.statusMark,
.commonMark,
.templateMark,
.replyMark {
  height: 24px;
  line-height: 24px;
}
.templateMark,
.nextMark,
.replyMark {
  color: black;
  font-size: 14px;
  margin: 16px 0;
}
.templateMark::after {
  content: '';
  display: block;
  width: 88%;
  border: 1px dashed #ccc;
  top: 50%;
  position: absolute;
  left: 10%;
}
.nextMark::after {
  content: '';
  display: block;
  width: 85%;
  border: 1px dashed #ccc;
  top: 50%;
  position: absolute;
  left: 13%;
}
.replyMark::after {
  content: '';
  display: block;
  width: 89%;
  top: 50%;
  position: absolute;
  left: 9%;
  border: 1px dashed #ccc;
}
/deep/ .realValue {
  color: #0d0c0c;
}
/deep/ .ant-select {
  font-size: 12px;
}
.nextFlower {
  width: 240px;
}
.statusMark {
  border-left: 4px solid rgba(253, 200, 120, 1);
}
// .templateMark {
//   border-left: 4px solid rgba(255, 95, 121, 1);
// }
// .replyMark {
//   border-left: 4px solid rgba(43, 158, 251, 1);
// }
.myIcon {
  margin-right: 4px;
}
// /deep/ .ant-select, .ant-cascader-input.ant-input {
//   font-size: 12px;
// }
</style>
